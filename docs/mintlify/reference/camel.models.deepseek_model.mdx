<a id="camel.models.deepseek_model"></a>

<a id="camel.models.deepseek_model.DeepSeekModel"></a>

## DeepSeekModel

```python
class DeepSeekModel(OpenAICompatibleModel):
```

DeepSeek API in a unified OpenAICompatibleModel interface.

**Parameters:**

- **model_type** (Union[ModelType, str]): Model for which a backend is created.
- **model_config_dict** (Optional[Dict[str, Any]], optional): A dictionary that will be fed into:obj:`openai.ChatCompletion.create()`. If :obj:`None`, :obj:`DeepSeekConfig().as_dict()` will be used. (default: :obj:`None`)
- **api_key** (Optional[str], optional): The API key for authenticating with the DeepSeek service. (default: :obj:`None`)
- **url** (Optional[str], optional): The url to the DeepSeek service. (default: :obj:`https://api.deepseek.com`)
- **token_counter** (Optional[BaseTokenCounter], optional): Token counter to use for the model. If not provided, :obj:`OpenAITokenCounter` will be used. (default: :obj:`None`)
- **timeout** (Optional[float], optional): The timeout value in seconds for API calls. If not provided, will fall back to the MODEL_TIMEOUT environment variable or default to 180 seconds. (default: :obj:`None`)
- **max_retries** (int, optional): Maximum number of retries for API calls. (default: :obj:`3`) **kwargs (Any): Additional arguments to pass to the client initialization.
- **References**: 
- **https**: //api-docs.deepseek.com/

<a id="camel.models.deepseek_model.DeepSeekModel.__init__"></a>

### __init__

```python
def __init__(
    self,
    model_type: Union[ModelType, str],
    model_config_dict: Optional[Dict[str, Any]] = None,
    api_key: Optional[str] = None,
    url: Optional[str] = None,
    token_counter: Optional[BaseTokenCounter] = None,
    timeout: Optional[float] = None,
    max_retries: int = 3,
    **kwargs: Any
):
```

<a id="camel.models.deepseek_model.DeepSeekModel._prepare_request"></a>

### _prepare_request

```python
def _prepare_request(
    self,
    messages: List[OpenAIMessage],
    response_format: Optional[Type[BaseModel]] = None,
    tools: Optional[List[Dict[str, Any]]] = None
):
```

<a id="camel.models.deepseek_model.DeepSeekModel._post_handle_response"></a>

### _post_handle_response

```python
def _post_handle_response(self, response: ChatCompletion):
```

Handle reasoning content with `<think>` tags at the beginning.

<a id="camel.models.deepseek_model.DeepSeekModel._run"></a>

### _run

```python
def _run(
    self,
    messages: List[OpenAIMessage],
    response_format: Optional[Type[BaseModel]] = None,
    tools: Optional[List[Dict[str, Any]]] = None
):
```

Runs inference of DeepSeek chat completion.

**Parameters:**

- **messages** (List[OpenAIMessage]): Message list with the chat history in OpenAI API format.

**Returns:**

  Union[ChatCompletion, Stream[ChatCompletionChunk]]:
`ChatCompletion` in the non-stream mode, or
`Stream[ChatCompletionChunk]` in the stream mode.
