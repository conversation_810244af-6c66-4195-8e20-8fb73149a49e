<a id="camel.models.mistral_model"></a>

<a id="camel.models.mistral_model.MistralModel"></a>

## MistralModel

```python
class MistralModel(BaseModelBackend):
```

Mistral API in a unified BaseModelBackend interface.

**Parameters:**

- **model_type** (Union[ModelType, str]): Model for which a backend is created, one of MISTRAL_* series.
- **model_config_dict** (Optional[Dict[str, Any]], optional): A dictionary that will be fed into:obj:`Mistral.chat.complete()`.
- **If**: obj:`None`, :obj:`MistralConfig().as_dict()` will be used. (default: :obj:`None`)
- **api_key** (Optional[str], optional): The API key for authenticating with the mistral service. (default: :obj:`None`)
- **url** (Optional[str], optional): The url to the mistral service. (default: :obj:`None`)
- **token_counter** (Optional[BaseTokenCounter], optional): Token counter to use for the model. If not provided, :obj:`OpenAITokenCounter` will be used. (default: :obj:`None`)
- **timeout** (Optional[float], optional): The timeout value in seconds for API calls. If not provided, will fall back to the MODEL_TIMEOUT environment variable or default to 180 seconds. (default: :obj:`None`)
- **max_retries** (int, optional): Maximum number of retries for API calls. (default: :obj:`3`) **kwargs (Any): Additional arguments to pass to the client initialization.

<a id="camel.models.mistral_model.MistralModel.__init__"></a>

### __init__

```python
def __init__(
    self,
    model_type: Union[ModelType, str],
    model_config_dict: Optional[Dict[str, Any]] = None,
    api_key: Optional[str] = None,
    url: Optional[str] = None,
    token_counter: Optional[BaseTokenCounter] = None,
    timeout: Optional[float] = None,
    max_retries: int = 3,
    **kwargs: Any
):
```

<a id="camel.models.mistral_model.MistralModel._to_openai_response"></a>

### _to_openai_response

```python
def _to_openai_response(self, response: 'ChatCompletionResponse'):
```

<a id="camel.models.mistral_model.MistralModel._to_mistral_chatmessage"></a>

### _to_mistral_chatmessage

```python
def _to_mistral_chatmessage(self, messages: List[OpenAIMessage]):
```

<a id="camel.models.mistral_model.MistralModel.token_counter"></a>

### token_counter

```python
def token_counter(self):
```

**Returns:**

  BaseTokenCounter: The token counter following the model's
tokenization style.

<a id="camel.models.mistral_model.MistralModel._run"></a>

### _run

```python
def _run(
    self,
    messages: List[OpenAIMessage],
    response_format: Optional[Type[BaseModel]] = None,
    tools: Optional[List[Dict[str, Any]]] = None
):
```

Runs inference of Mistral chat completion.

**Parameters:**

- **messages** (List[OpenAIMessage]): Message list with the chat history in OpenAI API format.
- **response_format** (Optional[Type[BaseModel]]): The format of the response for this query.
- **tools** (Optional[List[Dict[str, Any]]]): The tools to use for this query.

**Returns:**

  ChatCompletion: The response from the model.

<a id="camel.models.mistral_model.MistralModel._prepare_request"></a>

### _prepare_request

```python
def _prepare_request(
    self,
    messages: List[OpenAIMessage],
    response_format: Optional[Type[BaseModel]] = None,
    tools: Optional[List[Dict[str, Any]]] = None
):
```

<a id="camel.models.mistral_model.MistralModel.stream"></a>

### stream

```python
def stream(self):
```

**Returns:**

  bool: Whether the model is in stream mode.
