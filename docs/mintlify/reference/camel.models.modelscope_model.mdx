<a id="camel.models.modelscope_model"></a>

<a id="camel.models.modelscope_model.ModelScopeModel"></a>

## ModelScopeModel

```python
class ModelScopeModel(OpenAICompatibleModel):
```

ModelScope API in a unified OpenAICompatibleModel interface.

**Parameters:**

- **model_type** (Union[ModelType, str]): Model for which a backend is created, one of ModelScope series.
- **model_config_dict** (Optional[Dict[str, Any]], optional): A dictionary that will be fed into:obj:`openai.ChatCompletion.create()`. If :obj:`None`, :obj:`ModelScopeConfig().as_dict()` will be used. (default: :obj:`None`)
- **api_key** (Optional[str], optional): The MODELSCOPE_SDK_TOKEN for authenticating with the ModelScope service. (default: :obj:`None`) refer to the following link for more details:
- **https**: //modelscope.cn/my/myaccesstoken
- **url** (Optional[str], optional): The url to the ModelScope service. (default: :obj:`https://api-inference.modelscope.cn/v1/`)
- **token_counter** (Optional[BaseTokenCounter], optional): Token counter to use for the model. If not provided, :obj:`OpenAITokenCounter( ModelType.GPT_4O_MINI)` will be used. (default: :obj:`None`)
- **timeout** (Optional[float], optional): The timeout value in seconds for API calls. If not provided, will fall back to the MODEL_TIMEOUT environment variable or default to 180 seconds. (default: :obj:`None`)
- **max_retries** (int, optional): Maximum number of retries for API calls. (default: :obj:`3`) **kwargs (Any): Additional arguments to pass to the client initialization.

<a id="camel.models.modelscope_model.ModelScopeModel.__init__"></a>

### __init__

```python
def __init__(
    self,
    model_type: Union[ModelType, str],
    model_config_dict: Optional[Dict[str, Any]] = None,
    api_key: Optional[str] = None,
    url: Optional[str] = None,
    token_counter: Optional[BaseTokenCounter] = None,
    timeout: Optional[float] = None,
    max_retries: int = 3,
    **kwargs: Any
):
```

<a id="camel.models.modelscope_model.ModelScopeModel._post_handle_response"></a>

### _post_handle_response

```python
def _post_handle_response(
    self,
    response: Union[ChatCompletion, Stream[ChatCompletionChunk]]
):
```

Handle reasoning content with `<think>` tags at the beginning.

<a id="camel.models.modelscope_model.ModelScopeModel._request_chat_completion"></a>

### _request_chat_completion

```python
def _request_chat_completion(
    self,
    messages: List[OpenAIMessage],
    tools: Optional[List[Dict[str, Any]]] = None
):
```
