<a id="camel.models.nemotron_model"></a>

<a id="camel.models.nemotron_model.NemotronModel"></a>

## NemotronModel

```python
class NemotronModel(OpenAICompatibleModel):
```

Nemotron model API backend with OpenAI compatibility.

**Parameters:**

- **model_type** (Union[ModelType, str]): Model for which a backend is created.
- **api_key** (Optional[str], optional): The API key for authenticating with the Nvidia service. (default: :obj:`None`)
- **url** (Optional[str], optional): The url to the Nvidia service. (default: :obj:`https://integrate.api.nvidia.com/v1`)
- **timeout** (Optional[float], optional): The timeout value in seconds for API calls. If not provided, will fall back to the MODEL_TIMEOUT environment variable or default to 180 seconds. (default: :obj:`None`)
- **max_retries** (int, optional): Maximum number of retries for API calls. (default: :obj:`3`) **kwargs (Any): Additional arguments to pass to the client initialization.

**Note:**

Nemotron model doesn't support additional model config like OpenAI.

<a id="camel.models.nemotron_model.NemotronModel.__init__"></a>

### __init__

```python
def __init__(
    self,
    model_type: Union[ModelType, str],
    api_key: Optional[str] = None,
    url: Optional[str] = None,
    timeout: Optional[float] = None,
    max_retries: int = 3,
    **kwargs: Any
):
```

<a id="camel.models.nemotron_model.NemotronModel.token_counter"></a>

### token_counter

```python
def token_counter(self):
```
