<a id="camel.models.moonshot_model"></a>

<a id="camel.models.moonshot_model.MoonshotModel"></a>

## MoonshotModel

```python
class MoonshotModel(OpenAICompatibleModel):
```

Moonshot API in a unified OpenAICompatibleModel interface.

**Parameters:**

- **model_type** (Union[ModelType, str]): Model for which a backend is created, one of Moonshot series.
- **model_config_dict** (Optional[Dict[str, Any]], optional): A dictionary that will be fed into :obj:`openai.ChatCompletion.create()`. If :obj:`None`, :obj:`MoonshotConfig().as_dict()` will be used. (default: :obj:`None`)
- **api_key** (Optional[str], optional): The API key for authenticating with the Moonshot service. (default: :obj:`None`)
- **url** (Optional[str], optional): The url to the Moonshot service. (default: :obj:`https://api.moonshot.cn/v1`)
- **token_counter** (Optional[BaseTokenCounter], optional): Token counter to use for the model. If not provided, :obj:`OpenAITokenCounter( ModelType.GPT_4)` will be used. (default: :obj:`None`)
- **timeout** (Optional[float], optional): The timeout value in seconds for API calls. If not provided, will fall back to the MODEL_TIMEOUT environment variable or default to 180 seconds. (default: :obj:`None`)
- **max_retries** (int, optional): Maximum number of retries for API calls. (default: :obj:`3`) **kwargs (Any): Additional arguments to pass to the client initialization.

<a id="camel.models.moonshot_model.MoonshotModel.__init__"></a>

### __init__

```python
def __init__(
    self,
    model_type: Union[ModelType, str],
    model_config_dict: Optional[Dict[str, Any]] = None,
    api_key: Optional[str] = None,
    url: Optional[str] = None,
    token_counter: Optional[BaseTokenCounter] = None,
    timeout: Optional[float] = None,
    max_retries: int = 3,
    **kwargs: Any
):
```

<a id="camel.models.moonshot_model.MoonshotModel._prepare_request"></a>

### _prepare_request

```python
def _prepare_request(
    self,
    messages: List[OpenAIMessage],
    response_format: Optional[Type[BaseModel]] = None,
    tools: Optional[List[Dict[str, Any]]] = None
):
```

Prepare the request configuration for Moonshot API.

**Parameters:**

- **messages** (List[OpenAIMessage]): Message list with the chat history in OpenAI API format.
- **response_format** (Optional[Type[BaseModel]]): The format of the response.
- **tools** (Optional[List[Dict[str, Any]]]): The schema of the tools to use for the request.

**Returns:**

  Dict[str, Any]: The prepared request configuration.
