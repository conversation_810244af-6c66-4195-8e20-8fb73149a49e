<a id="camel.models.watsonx_model"></a>

<a id="camel.models.watsonx_model.WatsonXModel"></a>

## WatsonXModel

```python
class WatsonXModel(BaseModelBackend):
```

WatsonX API in a unified BaseModelBackend interface.

**Parameters:**

- **model_type** (Union[ModelType, str]): Model type for which a backend is created, one of WatsonX series.
- **model_config_dict** (Optional[Dict[str, Any]], optional): A dictionary that will be fed into :obj:`ModelInference.chat()`.
- **If**: obj:`None`, :obj:`WatsonXConfig().as_dict()` will be used. (default: :obj:`None`)
- **api_key** (Optional[str], optional): The API key for authenticating with the WatsonX service. (default: :obj:`None`)
- **url** (Optional[str], optional): The url to the WatsonX service. (default: :obj:`None`)
- **project_id** (Optional[str], optional): The project ID authenticating with the WatsonX service. (default: :obj:`None`)
- **token_counter** (Optional[BaseTokenCounter], optional): Token counter to use for the model. If not provided, :obj:`OpenAITokenCounter( ModelType.GPT_4O_MINI)` will be used. (default: :obj:`None`)
- **timeout** (Optional[float], optional): The timeout value in seconds for API calls. If not provided, will fall back to the MODEL_TIMEOUT environment variable or default to 180 seconds. (default: :obj:`None`) **kwargs (Any): Additional arguments to pass to the client initialization.

<a id="camel.models.watsonx_model.WatsonXModel.__init__"></a>

### __init__

```python
def __init__(
    self,
    model_type: Union[ModelType, str],
    model_config_dict: Optional[Dict[str, Any]] = None,
    api_key: Optional[str] = None,
    url: Optional[str] = None,
    project_id: Optional[str] = None,
    token_counter: Optional[BaseTokenCounter] = None,
    timeout: Optional[float] = None,
    **kwargs: Any
):
```

<a id="camel.models.watsonx_model.WatsonXModel._to_openai_response"></a>

### _to_openai_response

```python
def _to_openai_response(self, response: Dict[str, Any]):
```

Convert WatsonX response to OpenAI format.

<a id="camel.models.watsonx_model.WatsonXModel.token_counter"></a>

### token_counter

```python
def token_counter(self):
```

**Returns:**

  BaseTokenCounter: The token counter following the model's
tokenization style.

<a id="camel.models.watsonx_model.WatsonXModel._prepare_request"></a>

### _prepare_request

```python
def _prepare_request(
    self,
    messages: List[OpenAIMessage],
    response_format: Optional[Type[BaseModel]] = None,
    tools: Optional[List[Dict[str, Any]]] = None
):
```

<a id="camel.models.watsonx_model.WatsonXModel._run"></a>

### _run

```python
def _run(
    self,
    messages: List[OpenAIMessage],
    response_format: Optional[Type[BaseModel]] = None,
    tools: Optional[List[Dict[str, Any]]] = None
):
```

Runs inference of WatsonX chat completion.

**Parameters:**

- **messages** (List[OpenAIMessage]): Message list with the chat history in OpenAI API format.
- **response_format** (Optional[Type[BaseModel]], optional): The response format. (default: :obj:`None`)
- **tools** (Optional[List[Dict[str, Any]]], optional): tools to use. (default: :obj:`None`)

**Returns:**

  ChatCompletion.

<a id="camel.models.watsonx_model.WatsonXModel.stream"></a>

### stream

```python
def stream(self):
```

**Returns:**

  bool: Whether the model is in stream mode.
